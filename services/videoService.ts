import { FFmpeg } from '@ffmpeg/ffmpeg';
import { fetchFile, toBlobURL } from '@ffmpeg/util';
import { MergedSubtitle, VideoProcessingOptions, VideoProcessingProgress, ProcessedVideoResult, SubtitleStyle } from '../types';
import { generateSRTContent } from '../utils/subtitleUtils';

class VideoService {
  private ffmpeg: FFmpeg | null = null;
  private isLoaded = false;
  private progressCallback: ((progress: VideoProcessingProgress) => void) | null = null;

  constructor() {
    this.ffmpeg = new FFmpeg();
  }

  setProgressCallback(callback: (progress: VideoProcessingProgress) => void) {
    this.progressCallback = callback;
  }

  private updateProgress(stage: VideoProcessingProgress['stage'], progress: number, message: string, error?: string) {
    if (this.progressCallback) {
      this.progressCallback({ stage, progress, message, error });
    }
  }

  async loadFFmpeg(): Promise<void> {
    if (this.isLoaded || !this.ffmpeg) return;

    try {
      this.updateProgress('loading_ffmpeg', 10, 'Loading FFmpeg...');

      // Load FFmpeg with CDN URLs
      const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';

      this.ffmpeg.on('log', ({ message }) => {
        console.log('FFmpeg log:', message);
      });

      this.ffmpeg.on('progress', ({ progress }) => {
        if (progress > 0) {
          this.updateProgress('processing_video', Math.round(progress * 100), `Processing video... ${Math.round(progress * 100)}%`);
        }
      });

      await this.ffmpeg.load({
        coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
        wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
      });

      this.isLoaded = true;
      this.updateProgress('loading_ffmpeg', 30, 'FFmpeg loaded successfully');
    } catch (error) {
      console.error('Failed to load FFmpeg:', error);
      this.updateProgress('error', 0, 'Failed to load FFmpeg', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  private generateSubtitleFilter(subtitles: MergedSubtitle[], type: 'original' | 'translated', style: SubtitleStyle): string {
    const subtitleLines = subtitles
      .filter(sub => type === 'original' ? sub.originalText.trim() : sub.translatedText.trim())
      .map(sub => ({
        id: sub.id,
        startTime: sub.startTime,
        endTime: sub.endTime,
        text: type === 'original' ? sub.originalText : sub.translatedText,
      }));

    if (subtitleLines.length === 0) return '';

    // Convert SRT time format to seconds for FFmpeg
    const timeToSeconds = (timeStr: string): number => {
      const [time, ms] = timeStr.split(',');
      const [hours, minutes, seconds] = time.split(':').map(Number);
      return hours * 3600 + minutes * 60 + seconds + Number(ms) / 1000;
    };

    // Generate drawtext filters for each subtitle
    const filters = subtitleLines.map((sub, index) => {
      const startTime = timeToSeconds(sub.startTime);
      const endTime = timeToSeconds(sub.endTime);
      // Escape text for FFmpeg - replace problematic characters
      const escapedText = sub.text
        .replace(/\\/g, '\\\\')
        .replace(/'/g, "\\'")
        .replace(/:/g, "\\:")
        .replace(/\n/g, '\\n')
        .replace(/\r/g, '');

      const yPosition = style.position === 'top' ? '50' :
                       style.position === 'center' ? '(h-text_h)/2' :
                       'h-text_h-50';

      // Use a more compatible approach without specific font file paths
      let filter = `drawtext=text='${escapedText}':fontsize=${style.fontSize}:fontcolor=${style.fontColor}:x=(w-text_w)/2:y=${yPosition}:enable='between(t,${startTime},${endTime})'`;

      if (style.outline) {
        filter += `:borderw=2:bordercolor=${style.outlineColor}`;
      }

      return filter;
    });

    return filters.join(',');
  }

  async processVideoWithSubtitles(
    videoFile: File,
    subtitles: MergedSubtitle[],
    options: VideoProcessingOptions
  ): Promise<ProcessedVideoResult> {
    if (!this.ffmpeg) {
      throw new Error('FFmpeg not initialized');
    }

    try {
      this.updateProgress('initializing', 5, 'Initializing video processing...');

      if (!this.isLoaded) {
        await this.loadFFmpeg();
      }

      this.updateProgress('processing_video', 40, 'Loading video file...');

      // Write input video to FFmpeg filesystem
      const fileExtension = videoFile.name.split('.').pop()?.toLowerCase() || 'mp4';
      const inputFileName = `input.${fileExtension}`;

      this.updateProgress('processing_video', 45, 'Loading video into FFmpeg...');
      await this.ffmpeg.writeFile(inputFileName, await fetchFile(videoFile));

      // Prepare subtitle filters
      const filters: string[] = [];

      if (options.embedOriginalSubtitles) {
        const originalFilter = this.generateSubtitleFilter(subtitles, 'original', options.subtitleStyle);
        if (originalFilter) filters.push(originalFilter);
      }

      if (options.embedTranslatedSubtitles) {
        const translatedFilter = this.generateSubtitleFilter(subtitles, 'translated', {
          ...options.subtitleStyle,
          position: options.embedOriginalSubtitles ? 'top' : options.subtitleStyle.position
        });
        if (translatedFilter) filters.push(translatedFilter);
      }

      this.updateProgress('embedding_subtitles', 60, 'Embedding subtitles...');

      const outputFileName = `output.${options.outputFormat}`;

      if (filters.length > 0) {
        // Apply subtitle filters
        const filterComplex = filters.join(',');
        console.log('Applying filter:', filterComplex);

        await this.ffmpeg.exec([
          '-i', inputFileName,
          '-vf', filterComplex,
          '-c:a', 'copy',
          '-preset', 'ultrafast', // Use fastest encoding preset
          '-y',
          outputFileName
        ]);
      } else {
        // No subtitles to embed, just copy the video
        await this.ffmpeg.exec([
          '-i', inputFileName,
          '-c', 'copy',
          '-y',
          outputFileName
        ]);
      }

      this.updateProgress('finalizing', 90, 'Finalizing video...');

      // Read the output file
      const outputData = await this.ffmpeg.readFile(outputFileName);
      const videoBlob = new Blob([outputData], { type: `video/${options.outputFormat}` });

      // Clean up
      await this.ffmpeg.deleteFile(inputFileName);
      await this.ffmpeg.deleteFile(outputFileName);

      const result: ProcessedVideoResult = {
        videoBlob,
        filename: `${videoFile.name.split('.')[0]}_with_subtitles.${options.outputFormat}`,
        duration: 0, // We could get this from ffprobe if needed
        size: videoBlob.size
      };

      this.updateProgress('completed', 100, 'Video processing completed successfully');
      return result;

    } catch (error) {
      console.error('Video processing error:', error);
      this.updateProgress('error', 0, 'Video processing failed', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  async createSubtitleOnlyVideo(
    subtitles: MergedSubtitle[],
    options: VideoProcessingOptions,
    duration: number = 60
  ): Promise<ProcessedVideoResult> {
    if (!this.ffmpeg) {
      throw new Error('FFmpeg not initialized');
    }

    try {
      this.updateProgress('initializing', 5, 'Creating subtitle-only video...');

      if (!this.isLoaded) {
        await this.loadFFmpeg();
      }

      // Create a blank video with subtitles
      const filters: string[] = ['color=black:size=1920x1080:duration=' + duration];

      if (options.embedOriginalSubtitles) {
        const originalFilter = this.generateSubtitleFilter(subtitles, 'original', options.subtitleStyle);
        if (originalFilter) filters.push(originalFilter);
      }

      if (options.embedTranslatedSubtitles) {
        const translatedFilter = this.generateSubtitleFilter(subtitles, 'translated', {
          ...options.subtitleStyle,
          position: options.embedOriginalSubtitles ? 'top' : options.subtitleStyle.position
        });
        if (translatedFilter) filters.push(translatedFilter);
      }

      const outputFileName = `subtitle_video.${options.outputFormat}`;
      const filterComplex = filters.join(',');

      await this.ffmpeg.exec([
        '-f', 'lavfi',
        '-i', filterComplex,
        '-t', duration.toString(),
        '-y',
        outputFileName
      ]);

      const outputData = await this.ffmpeg.readFile(outputFileName);
      const videoBlob = new Blob([outputData], { type: `video/${options.outputFormat}` });

      await this.ffmpeg.deleteFile(outputFileName);

      const result: ProcessedVideoResult = {
        videoBlob,
        filename: `subtitles_only.${options.outputFormat}`,
        duration,
        size: videoBlob.size
      };

      this.updateProgress('completed', 100, 'Subtitle video created successfully');
      return result;

    } catch (error) {
      console.error('Subtitle video creation error:', error);
      this.updateProgress('error', 0, 'Subtitle video creation failed', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  terminate() {
    if (this.ffmpeg) {
      this.ffmpeg.terminate();
      this.isLoaded = false;
    }
  }
}

// Export a singleton instance
export const videoService = new VideoService();
export default videoService;
