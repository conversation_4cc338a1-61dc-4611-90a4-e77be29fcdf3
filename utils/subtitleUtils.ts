
import { MergedSubtitle, SubtitleLine } from '../types';

export const formatSecondsToSRTTime = (totalSeconds: number): string => {
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = Math.floor(totalSeconds % 60);
  const milliseconds = Math.floor((totalSeconds * 1000) % 1000);

  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')},${String(milliseconds).padStart(3, '0')}`;
};

const isValidSRTTimeFormat = (timeStr: string): boolean => {
  return /^\d{2}:\d{2}:\d{2},\d{3}$/.test(timeStr);
};

export const generateSRTContent = (subtitles: SubtitleLine[]): string => {
  let srtContent = '';
  subtitles.forEach((sub, index) => {
    if (!isValidSRTTimeFormat(sub.startTime) || !isValidSRTTimeFormat(sub.endTime)) {
      console.warn(`Invalid time format for subtitle ID ${sub.id}. Skipping SRT entry.`);
      return;
    }
    srtContent += `${index + 1}\n`;
    srtContent += `${sub.startTime} --> ${sub.endTime}\n`;
    srtContent += `${sub.text}\n\n`;
  });
  return srtContent;
};

export const downloadSRTFile = (srtContent: string, filename: string = 'subtitles.srt'): void => {
  const blob = new Blob([srtContent], { type: 'text/srt;charset=utf-8;' });
  const link = document.createElement('a');
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
};

// New functions for VTT generation
export const formatSRTTimeToVTTTime = (srtTime: string): string => {
  if (!isValidSRTTimeFormat(srtTime)) {
    // console.warn(`Invalid SRT time format for VTT conversion: ${srtTime}. Returning as is.`);
    return srtTime; // Or handle error appropriately
  }
  return srtTime.replace(',', '.');
};

export const generateVTTContent = (subtitles: MergedSubtitle[], type: 'original' | 'translated'): string => {
  let vttContent = 'WEBVTT\n\n';
  subtitles.forEach((sub, index) => {
    const text = type === 'original' ? sub.originalText : sub.translatedText;
    if (text.trim() === '') {
      return; // Skip empty subtitles
    }
    if (!isValidSRTTimeFormat(sub.startTime) || !isValidSRTTimeFormat(sub.endTime)) {
      console.warn(`Invalid time format for subtitle ID ${sub.id}. Skipping VTT cue.`);
      return;
    }
    vttContent += `${index + 1}\n`; // Optional cue identifier
    vttContent += `${formatSRTTimeToVTTTime(sub.startTime)} --> ${formatSRTTimeToVTTTime(sub.endTime)}\n`;
    vttContent += `${text}\n\n`;
  });
  return vttContent;
};

// Video processing utility functions
export const downloadVideoFile = (videoBlob: Blob, filename: string): void => {
  const link = document.createElement('a');
  if (link.download !== undefined) {
    const url = URL.createObjectURL(videoBlob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const getDefaultSubtitleStyle = () => ({
  fontFamily: 'Arial',
  fontSize: 24,
  fontColor: 'white',
  backgroundColor: 'rgba(0,0,0,0.8)',
  position: 'bottom' as const,
  outline: true,
  outlineColor: 'black'
});
