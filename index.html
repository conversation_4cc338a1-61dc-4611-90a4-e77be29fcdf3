
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI Subtitle Studio</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body {
      font-family: 'Inter', sans-serif;
    }
    /* Custom scrollbar for webkit browsers */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #1e293b; /* slate-800 */
      border-radius: 10px;
    }
    ::-webkit-scrollbar-thumb {
      background: #475569; /* slate-600 */
      border-radius: 10px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #64748b; /* slate-500 */
    }

    /* For Firefox */
    .thin-scrollbar {
      scrollbar-width: thin;
      scrollbar-color: #475569 #1e293b; /* thumb track */
    }
    
    /* Apply to specific elements if needed, e.g., .thin-scrollbar::-webkit-scrollbar */
    .thin-scrollbar::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    .thin-scrollbar::-webkit-scrollbar-thumb {
      background: #475569; /* slate-600 */
      border-radius: 10px;
    }
     .thin-scrollbar::-webkit-scrollbar-track {
      background: #0f172a; /* slate-900 or slightly darker than editor bg */
      border-radius: 10px;
    }
    .thin-scrollbar::-webkit-scrollbar-thumb:hover {
      background: #64748b; /* slate-500 */
    }
  </style>
<script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.0.1"
  }
}
</script>
</head>
<body class="bg-slate-900 text-slate-100">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>
<link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script>
